from django.urls import path, include
from rest_framework.response import Response
from rest_framework.decorators import api_view
from rest_framework.reverse import reverse

@api_view(['GET'])
def api_root(request, format=None):
    return Response({
        'store': reverse('store-api-root', request=request, format=format),
        # 'products': request.build_absolute_uri('/api/products/'),
        # 'orders': request.build_absolute_uri('/api/orders/'),
        # 'account': request.build_absolute_uri('/api/account/'),
        # 'carts': request.build_absolute_uri('/api/carts/'),
    })

urlpatterns = [
    path('', api_root, name='api-root'),
    path("auth/", include("djoser.urls")),
    path("auth/", include("djoser.urls.jwt")),
    path("auth/", include("djoser.social.urls")),
    # path("account/", include("account.urls")),
    # path("carts/", include("carts.urls")),
    # path("orders/", include("orders.urls")),
    # path("products/", include("products.urls")),
    path("store/", include("store.urls")),
]
