#!/usr/bin/env python3
"""
Test script for Store App API endpoints
Run this script to verify that all API endpoints are working correctly.
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
STORE_API_BASE = f"{BASE_URL}/store/api"

# Test data
TEST_ORGANIZATION = {
    "name": "Test Beauty Salon",
    "location": "Test City",
    "phone": "1234567890",
    "email": "<EMAIL>",
    "instagram": "https://instagram.com/test"
}

TEST_HERO_SLIDE = {
    "title": "Test Hero Slide",
    "description": "This is a test hero slide",
    "client": 100,
    "service": 5,
    "experience": 2
}

TEST_ABOUT_CONTENT = {
    "content_type": "story",
    "title": "Test Story",
    "content": "This is our test story content",
    "subtitle": "A test subtitle",
    "is_active": True
}

TEST_TESTIMONIAL = {
    "rating": 5
}

def print_test_result(test_name, success, message=""):
    """Print formatted test result"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if message:
        print(f"    {message}")

def test_endpoint(method, url, data=None, headers=None, expected_status=200):
    """Test an API endpoint"""
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=headers)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            return False, f"Unsupported method: {method}"
        
        if response.status_code == expected_status:
            return True, response.json() if response.content else {}
        else:
            return False, f"Expected {expected_status}, got {response.status_code}: {response.text}"
    
    except requests.exceptions.ConnectionError:
        return False, "Connection error - make sure Django server is running"
    except Exception as e:
        return False, f"Error: {str(e)}"

def test_organizations():
    """Test Organization endpoints"""
    print("\n🏢 Testing Organization Endpoints")
    
    # Test GET list
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/organizations/")
    print_test_result("GET /organizations/", success, 
                     f"Found {len(result) if isinstance(result, list) else 'N/A'} organizations" if success else result)
    
    # Test GET active organization
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/organizations/active/")
    print_test_result("GET /organizations/active/", success, result if not success else "")

def test_hero_slides():
    """Test Hero Slide endpoints"""
    print("\n🎠 Testing Hero Slide Endpoints")
    
    # Test GET list
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/hero-slides/")
    print_test_result("GET /hero-slides/", success, 
                     f"Found {len(result) if isinstance(result, list) else 'N/A'} slides" if success else result)

def test_about_content():
    """Test About Page Content endpoints"""
    print("\n📄 Testing About Content Endpoints")
    
    # Test GET list
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/about-content/")
    print_test_result("GET /about-content/", success, 
                     f"Found {len(result) if isinstance(result, list) else 'N/A'} content items" if success else result)
    
    # Test GET active content
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/about-content/active/")
    print_test_result("GET /about-content/active/", success, result if not success else "")
    
    # Test GET by type
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/about-content/by-type/?type=story")
    print_test_result("GET /about-content/by-type/?type=story", success, result if not success else "")

def test_testimonials():
    """Test Testimonial endpoints"""
    print("\n⭐ Testing Testimonial Endpoints")
    
    # Test GET list
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/testimonials/")
    print_test_result("GET /testimonials/", success, 
                     f"Found {len(result) if isinstance(result, list) else 'N/A'} testimonials" if success else result)
    
    # Test GET featured
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/testimonials/featured/")
    print_test_result("GET /testimonials/featured/", success, result if not success else "")
    
    # Test GET by rating
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/testimonials/by-rating/?rating=5")
    print_test_result("GET /testimonials/by-rating/?rating=5", success, result if not success else "")

def test_filtering_and_search():
    """Test filtering and search functionality"""
    print("\n🔍 Testing Filtering & Search")
    
    # Test organization search
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/organizations/?search=beauty")
    print_test_result("Organization search", success, result if not success else "")
    
    # Test about content filtering
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/about-content/?content_type=story")
    print_test_result("About content filtering", success, result if not success else "")
    
    # Test testimonial filtering
    success, result = test_endpoint("GET", f"{STORE_API_BASE}/testimonials/?rating=5")
    print_test_result("Testimonial filtering", success, result if not success else "")

def test_authentication_required():
    """Test endpoints that require authentication"""
    print("\n🔐 Testing Authentication Required Endpoints")
    
    # Test creating testimonial without auth (should fail)
    success, result = test_endpoint("POST", f"{STORE_API_BASE}/testimonials/", 
                                  data=TEST_TESTIMONIAL, expected_status=401)
    print_test_result("POST /testimonials/ (no auth)", success, "Correctly requires authentication" if success else result)

def main():
    """Run all tests"""
    print("🧪 Store App API Test Suite")
    print("=" * 50)
    print(f"Testing API at: {STORE_API_BASE}")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all test suites
    test_organizations()
    test_hero_slides()
    test_about_content()
    test_testimonials()
    test_filtering_and_search()
    test_authentication_required()
    
    print("\n" + "=" * 50)
    print("🏁 Test Suite Complete!")
    print("\n📝 Notes:")
    print("- Make sure Django server is running: python manage.py runserver")
    print("- Some tests may fail if no data exists in the database")
    print("- Authentication tests require valid JWT tokens")
    print("- Admin-only endpoints require staff user authentication")

if __name__ == "__main__":
    main()
