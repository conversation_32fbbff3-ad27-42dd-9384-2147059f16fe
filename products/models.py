from account.models import UserAccount
from django.db import models
from django.urls import reverse
from django_jsonform.models.fields import <PERSON><PERSON><PERSON><PERSON>
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db.models import Avg
from datetime import datetime, timedelta
from django.utils import timezone
# Create your models here.


class ProductCategory(models.Model):
    name = models.CharField(max_length=50, unique=True)

    # to make get link of the product according to the category

    def get_url(self):
        return reverse("product_by_category")

    def __str__(self):
        return self.name

    class Meta:
        db_table = "product_categories"
        ordering = ["name"]
        indexes = [
            models.Index(fields=["name"], name="idx_product_category_name"),
        ]


class Product(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    price = models.DecimalField(
        max_digits=10, decimal_places=2, validators=[MinValueValidator(0)]
    )

    category = models.ForeignKey(
        ProductCategory, on_delete=models.CASCADE, related_name="products"
    )
    rating = models.FloatField(
        validators=[MinValueValidator(0), MaxValueValidator(5)], default=0.0
    )
    in_stock = models.BooleanField(default=True)
    ingredients = JSONField(
        schema={
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "quantity": {"type": "string"},
                },
                "required": ["name", "quantity"],
            },
        },
        blank=True,
        null=True,
    )
    usage_instructions = models.TextField(blank=True, null=True)
    benefits = JSONField(
        schema={"type": "array", "items": {"type": "string"}}, blank=True, null=True
    )
    image = models.ImageField(upload_to="products/", blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        db_table = "products"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["name"], name="idx_product_name"),
            models.Index(fields=["category"], name="idx_product_category"),
            models.Index(fields=["rating"], name="idx_product_rating"),
            models.Index(fields=["price"], name="idx_product_price"),
            models.Index(fields=["in_stock"], name="idx_product_in_stock"),
        ]

    def __str__(self):
        return self.name

    # to get the specific product url inside the specific category

    def get_url(self):
        return reverse("product_detail", args=[self.category.slug, self.slug])

    # calculating the average rating of the product

    def averageRating(self):
        reviews = ReviewRating.objects.filter(product=self, status=True).aggregate(
            average=Avg("rating")
        )
        avg = 0
        if reviews["average"] is not None:
            avg = float(reviews["average"])
        return avg

    def reveiewCount(self):
        reviews = ReviewRating.objects.filter(product=self, status=True)
        i = 0
        for review in reviews:
            i = i + 1
        return i


class VariationManager(models.Manager):
    def color(self):
        return super(VariationManager, self).filter(
            variation_category="color", is_active=True
        )


variation_category_choices = (("color", "color"),)


class Variation(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    variation_category = models.CharField(
        max_length=100, choices=variation_category_choices
    )
    variation_value = models.CharField(max_length=100)
    is_active = models.BooleanField(default=True)
    created_date = models.DateTimeField(auto_now=True)

    objects = VariationManager()  # calling the above method

    def __str__(self):
        return self.variation_value


class OfferProducts(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    price = models.IntegerField(blank=True, null=True)  # discounted price
    is_active = models.BooleanField(default=True)  # status of the offer
    start_date = models.DateTimeField(auto_now_add=True)
    end_date = models.DateTimeField(blank=True, null=True)

    def save(self, *args, **kwargs):
        now = timezone.now()

        # Assign default 1-day validity if end_date not provided
        if not self.end_date:
            self.end_date = now + timedelta(days=1)

        # Auto-deactivate if expired
        if self.end_date <= now:
            self.is_active = False

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.product.product_name} - Offer"

    def price_after_discount(self):
        if self.price is None:
            return self.product.price
        return self.product.price - self.price

    def discount_percent(self):
        if not self.product.price or not self.price:
            return 0
        return round((self.price * 100) / self.product.price)


class ReviewRating(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    user = models.ForeignKey(UserAccount, on_delete=models.CASCADE)
    subject = models.CharField(max_length=100, blank=True)
    review = models.TextField(blank=True)
    rating = models.FloatField()
    ip = models.CharField(max_length=20, blank=True)
    status = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.subject
