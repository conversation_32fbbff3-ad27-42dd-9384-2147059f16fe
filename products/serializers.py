from rest_framework import serializers
from .models import ProductCategory, Product, Variation, OfferProducts, ReviewRating
from account.models import UserAccount


class ProductCategorySerializer(serializers.ModelSerializer):
    """Serializer for ProductCategory model"""
    product_count = serializers.SerializerMethodField()
    average_rating = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductCategory
        fields = ['id', 'name', 'product_count', 'average_rating']
        read_only_fields = ['id']

    def get_product_count(self, obj):
        """Get count of active products in this category"""
        return obj.products.filter(is_active=True).count()

    def get_average_rating(self, obj):
        """Get average rating of products in this category"""
        products = obj.products.filter(is_active=True)
        if products.exists():
            total_rating = sum(product.rating for product in products)
            return round(total_rating / products.count(), 2)
        return 0.0

    def validate_name(self, value):
        """Validate category name uniqueness"""
        if ProductCategory.objects.filter(name__iexact=value).exists():
            if not self.instance or self.instance.name.lower() != value.lower():
                raise serializers.ValidationError("Category with this name already exists")
        return value


class ProductCategoryListSerializer(serializers.ModelSerializer):
    """Optimized serializer for ProductCategory list view"""
    product_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductCategory
        fields = ['id', 'name', 'product_count']

    def get_product_count(self, obj):
        """Get count of active products in this category"""
        return getattr(obj, '_product_count', obj.products.filter(is_active=True).count())


class VariationSerializer(serializers.ModelSerializer):
    """Serializer for Variation model"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    variation_category_display = serializers.CharField(source='get_variation_category_display', read_only=True)
    
    class Meta:
        model = Variation
        fields = [
            'id', 'product', 'product_name', 'variation_category', 
            'variation_category_display', 'variation_value', 'is_active', 'created_date'
        ]
        read_only_fields = ['id', 'created_date']

    def validate(self, attrs):
        """Validate variation uniqueness for product"""
        product = attrs.get('product')
        variation_category = attrs.get('variation_category')
        variation_value = attrs.get('variation_value')
        
        if product and variation_category and variation_value:
            existing = Variation.objects.filter(
                product=product,
                variation_category=variation_category,
                variation_value=variation_value
            )
            
            if self.instance:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise serializers.ValidationError(
                    f"Variation '{variation_value}' already exists for this product in category '{variation_category}'"
                )
        
        return attrs


class VariationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Variation"""
    product_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = Variation
        fields = ['product_id', 'variation_category', 'variation_value', 'is_active']

    def validate_product_id(self, value):
        """Validate product exists and is active"""
        try:
            product = Product.objects.get(id=value)
            if not product.is_active:
                raise serializers.ValidationError("Cannot add variation to inactive product")
            return value
        except Product.DoesNotExist:
            raise serializers.ValidationError("Product does not exist")

    def create(self, validated_data):
        """Create variation with product"""
        product_id = validated_data.pop('product_id')
        product = Product.objects.get(id=product_id)
        return Variation.objects.create(product=product, **validated_data)


class VariationListSerializer(serializers.ModelSerializer):
    """Optimized serializer for Variation list view"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    variation_category_display = serializers.CharField(source='get_variation_category_display', read_only=True)
    
    class Meta:
        model = Variation
        fields = ['id', 'product_name', 'variation_category_display', 'variation_value', 'is_active']


class OfferProductsSerializer(serializers.ModelSerializer):
    """Serializer for OfferProducts model"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    original_price = serializers.DecimalField(source='product.price', max_digits=10, decimal_places=2, read_only=True)
    discounted_price = serializers.SerializerMethodField()
    discount_percentage = serializers.SerializerMethodField()
    is_expired = serializers.SerializerMethodField()
    
    class Meta:
        model = OfferProducts
        fields = [
            'id', 'product', 'product_name', 'price', 'original_price', 
            'discounted_price', 'discount_percentage', 'is_active', 
            'is_expired', 'start_date', 'end_date'
        ]
        read_only_fields = ['id', 'start_date']

    def get_discounted_price(self, obj):
        """Calculate discounted price"""
        return obj.price_after_discount()

    def get_discount_percentage(self, obj):
        """Calculate discount percentage"""
        return obj.discount_percent()

    def get_is_expired(self, obj):
        """Check if offer is expired"""
        from django.utils import timezone
        return obj.end_date and obj.end_date <= timezone.now()

    def validate_price(self, value):
        """Validate discount price"""
        if value is not None and value < 0:
            raise serializers.ValidationError("Discount price cannot be negative")
        return value

    def validate(self, attrs):
        """Cross-field validation"""
        product = attrs.get('product')
        discount_price = attrs.get('price')
        
        if product and discount_price is not None:
            if discount_price >= product.price:
                raise serializers.ValidationError(
                    "Discount price must be less than original product price"
                )
        
        return attrs


class OfferProductsCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating OfferProducts"""
    product_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = OfferProducts
        fields = ['product_id', 'price', 'is_active', 'end_date']

    def validate_product_id(self, value):
        """Validate product exists and is active"""
        try:
            product = Product.objects.get(id=value)
            if not product.is_active:
                raise serializers.ValidationError("Cannot create offer for inactive product")
            return value
        except Product.DoesNotExist:
            raise serializers.ValidationError("Product does not exist")

    def create(self, validated_data):
        """Create offer with product"""
        product_id = validated_data.pop('product_id')
        product = Product.objects.get(id=product_id)
        return OfferProducts.objects.create(product=product, **validated_data)


class OfferProductsListSerializer(serializers.ModelSerializer):
    """Optimized serializer for OfferProducts list view"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    discount_percentage = serializers.SerializerMethodField()
    is_expired = serializers.SerializerMethodField()
    
    class Meta:
        model = OfferProducts
        fields = ['id', 'product_name', 'discount_percentage', 'is_active', 'is_expired', 'end_date']

    def get_discount_percentage(self, obj):
        """Calculate discount percentage"""
        return obj.discount_percent()

    def get_is_expired(self, obj):
        """Check if offer is expired"""
        from django.utils import timezone
        return obj.end_date and obj.end_date <= timezone.now()


# Nested serializers for related models
class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user info for reviews"""
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = UserAccount
        fields = ['id', 'email', 'first_name', 'last_name', 'full_name']


class ReviewRatingSerializer(serializers.ModelSerializer):
    """Serializer for ReviewRating model"""
    user = UserBasicSerializer(read_only=True)
    product_name = serializers.CharField(source='product.name', read_only=True)
    
    class Meta:
        model = ReviewRating
        fields = [
            'id', 'product', 'product_name', 'user', 'subject', 'review', 
            'rating', 'ip', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'ip']

    def validate_rating(self, value):
        """Validate rating is within valid range"""
        if value < 0 or value > 5:
            raise serializers.ValidationError("Rating must be between 0 and 5")
        return value

    def validate(self, attrs):
        """Validate user hasn't already reviewed this product"""
        product = attrs.get('product')
        user = self.context['request'].user if self.context.get('request') else None
        
        if product and user and user.is_authenticated:
            existing_review = ReviewRating.objects.filter(product=product, user=user)
            
            if self.instance:
                existing_review = existing_review.exclude(pk=self.instance.pk)
            
            if existing_review.exists():
                raise serializers.ValidationError(
                    "You have already reviewed this product"
                )
        
        return attrs


class ReviewRatingCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating ReviewRating"""
    product_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = ReviewRating
        fields = ['product_id', 'subject', 'review', 'rating']

    def validate_product_id(self, value):
        """Validate product exists"""
        try:
            Product.objects.get(id=value)
            return value
        except Product.DoesNotExist:
            raise serializers.ValidationError("Product does not exist")

    def create(self, validated_data):
        """Create review with product and user"""
        product_id = validated_data.pop('product_id')
        product = Product.objects.get(id=product_id)
        user = self.context['request'].user
        
        # Get client IP
        request = self.context.get('request')
        ip = request.META.get('REMOTE_ADDR', '') if request else ''
        
        return ReviewRating.objects.create(
            product=product,
            user=user,
            ip=ip,
            **validated_data
        )


class ReviewRatingListSerializer(serializers.ModelSerializer):
    """Optimized serializer for ReviewRating list view"""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    product_name = serializers.CharField(source='product.name', read_only=True)

    class Meta:
        model = ReviewRating
        fields = ['id', 'product_name', 'user_name', 'subject', 'rating', 'status', 'created_at']


class ProductSerializer(serializers.ModelSerializer):
    """Serializer for Product model"""
    category = ProductCategorySerializer(read_only=True)
    variations = VariationSerializer(source='variation_set', many=True, read_only=True)
    offers = OfferProductsSerializer(source='offerproducts_set', many=True, read_only=True)
    reviews = ReviewRatingSerializer(source='reviewrating_set', many=True, read_only=True)
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()
    current_offer = serializers.SerializerMethodField()
    discounted_price = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'description', 'price', 'category', 'rating',
            'in_stock', 'ingredients', 'usage_instructions', 'benefits',
            'image', 'created_at', 'updated_at', 'is_active', 'variations',
            'offers', 'reviews', 'average_rating', 'review_count',
            'current_offer', 'discounted_price'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'rating']

    def get_average_rating(self, obj):
        """Get calculated average rating"""
        return obj.averageRating()

    def get_review_count(self, obj):
        """Get count of approved reviews"""
        return obj.reveiewCount()

    def get_current_offer(self, obj):
        """Get current active offer if any"""
        from django.utils import timezone
        current_offers = obj.offerproducts_set.filter(
            is_active=True,
            end_date__gt=timezone.now()
        ).first()

        if current_offers:
            return OfferProductsSerializer(current_offers).data
        return None

    def get_discounted_price(self, obj):
        """Get current discounted price if offer exists"""
        current_offer = self.get_current_offer(obj)
        if current_offer:
            return current_offer['discounted_price']
        return obj.price

    def validate_price(self, value):
        """Validate price is positive"""
        if value <= 0:
            raise serializers.ValidationError("Price must be greater than 0")
        return value

    def validate_rating(self, value):
        """Validate rating is within valid range"""
        if value < 0 or value > 5:
            raise serializers.ValidationError("Rating must be between 0 and 5")
        return value


class ProductCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Product"""
    category_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = Product
        fields = [
            'name', 'description', 'price', 'category_id', 'in_stock',
            'ingredients', 'usage_instructions', 'benefits', 'image', 'is_active'
        ]

    def validate_category_id(self, value):
        """Validate category exists"""
        try:
            ProductCategory.objects.get(id=value)
            return value
        except ProductCategory.DoesNotExist:
            raise serializers.ValidationError("Category does not exist")

    def create(self, validated_data):
        """Create product with category"""
        category_id = validated_data.pop('category_id')
        category = ProductCategory.objects.get(id=category_id)
        return Product.objects.create(category=category, **validated_data)


class ProductListSerializer(serializers.ModelSerializer):
    """Optimized serializer for Product list view"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()
    has_offer = serializers.SerializerMethodField()
    discounted_price = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'price', 'category_name', 'rating', 'average_rating',
            'review_count', 'in_stock', 'image', 'is_active', 'has_offer',
            'discounted_price', 'created_at'
        ]

    def get_average_rating(self, obj):
        """Get calculated average rating"""
        return getattr(obj, '_avg_rating', obj.averageRating())

    def get_review_count(self, obj):
        """Get count of approved reviews"""
        return getattr(obj, '_review_count', obj.reveiewCount())

    def get_has_offer(self, obj):
        """Check if product has active offers"""
        from django.utils import timezone
        return obj.offerproducts_set.filter(
            is_active=True,
            end_date__gt=timezone.now()
        ).exists()

    def get_discounted_price(self, obj):
        """Get current discounted price if offer exists"""
        from django.utils import timezone
        current_offer = obj.offerproducts_set.filter(
            is_active=True,
            end_date__gt=timezone.now()
        ).first()

        if current_offer:
            return current_offer.price_after_discount()
        return obj.price


class ProductBasicSerializer(serializers.ModelSerializer):
    """Basic product info for other serializers"""
    category_name = serializers.CharField(source='category.name', read_only=True)

    class Meta:
        model = Product
        fields = ['id', 'name', 'price', 'image', 'category_name', 'in_stock', 'is_active']
