from django.core.management.base import BaseCommand
from django.utils import timezone
from products.models import OfferProducts


class Command(BaseCommand):
    help = "Deactivate expired offer products"

    def handle(self, *args, **kwargs):
        now = timezone.now()
        expired_offers = OfferProducts.objects.filter(is_active=True, end_date__lt=now)
        count = expired_offers.update(is_active=False)

        self.stdout.write(self.style.SUCCESS(f"{count} expired offer(s) deactivated."))
